#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的灯光强度分析脚本
不依赖外部shapefile，专注于灯光数据可视化和分析
"""

import numpy as np
import rasterio
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm, PowerNorm, LinearSegmentedColormap
from matplotlib.patches import Patch
import os
import warnings
warnings.filterwarnings('ignore')

def setup_matplotlib():
    """配置matplotlib"""
    plt.switch_backend('Agg')
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        print("中文字体设置成功")
    except:
        print("中文字体设置失败")

def analyze_light_distribution(data):
    """详细分析灯光数据分布"""
    total_pixels = data.size
    zero_pixels = np.sum(data == 0)
    non_zero_data = data[data > 0]
    
    analysis = {
        'shape': data.shape,
        'total_pixels': total_pixels,
        'zero_pixels': zero_pixels,
        'zero_ratio': (zero_pixels / total_pixels) * 100,
        'non_zero_count': len(non_zero_data),
        'min_val': np.min(data),
        'max_val': np.max(data),
        'mean_val': np.mean(data),
        'std_val': np.std(data)
    }
    
    if len(non_zero_data) > 0:
        analysis.update({
            'min_nonzero': np.min(non_zero_data),
            'max_nonzero': np.max(non_zero_data),
            'mean_nonzero': np.mean(non_zero_data),
            'std_nonzero': np.std(non_zero_data),
            'percentiles': {
                '25': np.percentile(non_zero_data, 25),
                '50': np.percentile(non_zero_data, 50),
                '75': np.percentile(non_zero_data, 75),
                '90': np.percentile(non_zero_data, 90),
                '95': np.percentile(non_zero_data, 95),
                '99': np.percentile(non_zero_data, 99)
            }
        })
    
    return analysis

def classify_business_potential(data):
    """基于灯光强度分类商业潜力"""
    non_zero_data = data[data > 0]
    
    if len(non_zero_data) == 0:
        return np.zeros_like(data), {'low': 0, 'medium': 0, 'high': 0}
    
    # 使用三分位数确定阈值
    low_threshold = np.percentile(non_zero_data, 33)
    high_threshold = np.percentile(non_zero_data, 67)
    
    # 创建分类数组
    classified = np.zeros_like(data, dtype=int)
    classified[data == 0] = 0  # 无灯光
    classified[(data > 0) & (data < low_threshold)] = 1  # 低潜力
    classified[(data >= low_threshold) & (data < high_threshold)] = 2  # 中等潜力
    classified[data >= high_threshold] = 3  # 高潜力
    
    # 计算各类别占比
    total_pixels = data.size
    percentages = {
        'no_light': np.sum(classified == 0) / total_pixels * 100,
        'low': np.sum(classified == 1) / total_pixels * 100,
        'medium': np.sum(classified == 2) / total_pixels * 100,
        'high': np.sum(classified == 3) / total_pixels * 100
    }
    
    thresholds = {
        'low_threshold': low_threshold,
        'high_threshold': high_threshold
    }
    
    return classified, percentages, thresholds

def create_enhanced_visualization(data, output_dir='output_images'):
    """创建增强的可视化分析"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 分析数据
    analysis = analyze_light_distribution(data)
    classified, percentages, thresholds = classify_business_potential(data)
    
    print("=== 详细数据分析 ===")
    print(f"数据形状: {analysis['shape']}")
    print(f"总像素数: {analysis['total_pixels']:,}")
    print(f"零值像素: {analysis['zero_pixels']:,} ({analysis['zero_ratio']:.1f}%)")
    print(f"有效像素: {analysis['non_zero_count']:,}")
    print(f"数值范围: {analysis['min_val']:.2f} - {analysis['max_val']:.2f}")
    
    if 'min_nonzero' in analysis:
        print(f"非零范围: {analysis['min_nonzero']:.2f} - {analysis['max_nonzero']:.2f}")
        print(f"非零平均: {analysis['mean_nonzero']:.2f} ± {analysis['std_nonzero']:.2f}")
        print(f"分位数: 25%={analysis['percentiles']['25']:.1f}, "
              f"50%={analysis['percentiles']['50']:.1f}, "
              f"75%={analysis['percentiles']['75']:.1f}, "
              f"95%={analysis['percentiles']['95']:.1f}")
    
    print(f"\n=== 商业潜力分类 ===")
    print(f"分类阈值: 低潜力 < {thresholds['low_threshold']:.1f}, "
          f"高潜力 ≥ {thresholds['high_threshold']:.1f}")
    print(f"无灯光: {percentages['no_light']:.1f}%")
    print(f"低潜力: {percentages['low']:.1f}%")
    print(f"中等潜力: {percentages['medium']:.1f}%")
    print(f"高潜力: {percentages['high']:.1f}%")
    
    # 创建主要分析图
    fig = plt.figure(figsize=(20, 16))
    
    # 使用GridSpec创建复杂布局
    gs = fig.add_gridspec(3, 3, height_ratios=[1, 1, 0.8], width_ratios=[1, 1, 1],
                         hspace=0.3, wspace=0.3)
    
    # 1. 原始问题展示
    ax1 = fig.add_subplot(gs[0, 0])
    im1 = ax1.imshow(data, cmap='viridis', vmin=0, vmax=analysis['max_val'])
    ax1.set_title('问题: 原始线性刻度\n(细节被压缩)', fontsize=12, fontweight='bold')
    plt.colorbar(im1, ax=ax1, shrink=0.8)
    ax1.set_xticks([])
    ax1.set_yticks([])
    
    # 2. 解决方案1: 95%截断
    ax2 = fig.add_subplot(gs[0, 1])
    if 'percentiles' in analysis:
        vmax_95 = analysis['percentiles']['95']
        im2 = ax2.imshow(data, cmap='plasma', vmin=0, vmax=vmax_95, interpolation='bilinear')
        ax2.set_title(f'解决方案: 95%截断\n(范围: 0-{vmax_95:.1f})', fontsize=12, fontweight='bold')
    else:
        im2 = ax2.imshow(data, cmap='plasma')
        ax2.set_title('解决方案: 95%截断', fontsize=12, fontweight='bold')
    plt.colorbar(im2, ax=ax2, shrink=0.8)
    ax2.set_xticks([])
    ax2.set_yticks([])
    
    # 3. 解决方案2: 对数刻度
    ax3 = fig.add_subplot(gs[0, 2])
    if 'min_nonzero' in analysis and analysis['min_nonzero'] > 0:
        log_vmin = max(analysis['min_nonzero'] * 0.1, 0.01)
        data_log = data.copy()
        data_log[data_log <= 0] = log_vmin
        im3 = ax3.imshow(data_log, cmap='plasma', norm=LogNorm(vmin=log_vmin, vmax=analysis['max_val']))
        ax3.set_title('解决方案: 对数刻度\n(增强低值区域)', fontsize=12, fontweight='bold')
    else:
        im3 = ax3.imshow(data, cmap='plasma')
        ax3.set_title('解决方案: 对数刻度', fontsize=12, fontweight='bold')
    plt.colorbar(im3, ax=ax3, shrink=0.8)
    ax3.set_xticks([])
    ax3.set_yticks([])
    
    # 4. 商业潜力分类
    ax4 = fig.add_subplot(gs[1, 0])
    colors = ['#2b83ba', '#abdda4', '#fdae61', '#d7191c']
    cmap_potential = LinearSegmentedColormap.from_list('potential', colors, N=4)
    im4 = ax4.imshow(classified, cmap=cmap_potential, vmin=0, vmax=3)
    ax4.set_title('商业潜力分类', fontsize=12, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        Patch(facecolor='#2b83ba', label=f'无灯光 ({percentages["no_light"]:.1f}%)'),
        Patch(facecolor='#abdda4', label=f'低潜力 ({percentages["low"]:.1f}%)'),
        Patch(facecolor='#fdae61', label=f'中等潜力 ({percentages["medium"]:.1f}%)'),
        Patch(facecolor='#d7191c', label=f'高潜力 ({percentages["high"]:.1f}%)')
    ]
    ax4.legend(handles=legend_elements, loc='center left', bbox_to_anchor=(1, 0.5))
    ax4.set_xticks([])
    ax4.set_yticks([])
    
    # 5. 数据分布直方图
    ax5 = fig.add_subplot(gs[1, 1])
    if 'min_nonzero' in analysis:
        non_zero_data = data[data > 0]
        ax5.hist(non_zero_data, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax5.axvline(thresholds['low_threshold'], color='orange', linestyle='--', 
                   label=f'低阈值: {thresholds["low_threshold"]:.1f}')
        ax5.axvline(thresholds['high_threshold'], color='red', linestyle='--', 
                   label=f'高阈值: {thresholds["high_threshold"]:.1f}')
        ax5.set_xlabel('灯光强度')
        ax5.set_ylabel('像素数量')
        ax5.set_title('非零值分布直方图', fontsize=12, fontweight='bold')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
    
    # 6. 幂律刻度
    ax6 = fig.add_subplot(gs[1, 2])
    im6 = ax6.imshow(data, cmap='plasma', norm=PowerNorm(gamma=0.5), interpolation='bilinear')
    ax6.set_title('解决方案: 幂律刻度\n(γ=0.5)', fontsize=12, fontweight='bold')
    plt.colorbar(im6, ax=ax6, shrink=0.8)
    ax6.set_xticks([])
    ax6.set_yticks([])
    
    # 7. 统计信息表格
    ax7 = fig.add_subplot(gs[2, :])
    ax7.axis('off')
    
    # 创建统计信息文本
    stats_text = f"""
数据统计摘要:
• 总像素数: {analysis['total_pixels']:,} | 数据形状: {analysis['shape'][0]}×{analysis['shape'][1]}
• 零值比例: {analysis['zero_ratio']:.1f}% ({analysis['zero_pixels']:,} 像素)
• 有效数据: {analysis['non_zero_count']:,} 像素 ({100-analysis['zero_ratio']:.1f}%)
• 数值范围: {analysis['min_val']:.2f} - {analysis['max_val']:.2f}
"""
    
    if 'min_nonzero' in analysis:
        stats_text += f"""• 非零范围: {analysis['min_nonzero']:.2f} - {analysis['max_nonzero']:.2f}
• 非零平均: {analysis['mean_nonzero']:.2f} ± {analysis['std_nonzero']:.2f}
• 关键分位数: 25%={analysis['percentiles']['25']:.1f}, 50%={analysis['percentiles']['50']:.1f}, 75%={analysis['percentiles']['75']:.1f}, 95%={analysis['percentiles']['95']:.1f}

商业潜力分析:
• 分类阈值: 低潜力 < {thresholds['low_threshold']:.1f}, 中等潜力 < {thresholds['high_threshold']:.1f}, 高潜力 ≥ {thresholds['high_threshold']:.1f}
• 潜力分布: 无灯光 {percentages['no_light']:.1f}% | 低潜力 {percentages['low']:.1f}% | 中等潜力 {percentages['medium']:.1f}% | 高潜力 {percentages['high']:.1f}%

可视化建议:
• 推荐使用95%分位数截断方案 (显示范围: 0-{analysis['percentiles']['95']:.1f})
• 对于学术展示可使用对数刻度显示完整范围
• 避免使用原始线性刻度，会压缩大部分数据细节"""
    
    ax7.text(0.05, 0.95, stats_text, transform=ax7.transAxes, 
            verticalalignment='top', fontsize=11,
            bbox=dict(boxstyle='round,pad=1', facecolor='lightgray', alpha=0.8))
    
    plt.suptitle('灯光强度可视化完整分析报告', fontsize=18, fontweight='bold', y=0.98)
    
    # 保存完整分析图
    analysis_path = os.path.join(output_dir, '完整_灯光强度分析报告.png')
    plt.savefig(analysis_path, dpi=300, bbox_inches='tight')
    print(f'\n✓ 完整分析报告已保存: {analysis_path}')
    
    # 创建推荐方案的单独图
    plt.figure(figsize=(12, 10))
    
    if 'percentiles' in analysis:
        vmax_best = analysis['percentiles']['95']
        im_best = plt.imshow(data, cmap='plasma', vmin=0, vmax=vmax_best, interpolation='bilinear')
        plt.colorbar(im_best, label='灯光强度', shrink=0.8)
        plt.title(f'推荐方案: 95%分位数截断可视化\n'
                 f'显示范围: 0 - {vmax_best:.2f} (隐藏了极值以增强对比度)', 
                 fontsize=14, fontweight='bold')
        
        # 添加统计信息
        info_text = (f"数据概览:\n"
                    f"• 总像素: {analysis['total_pixels']:,}\n"
                    f"• 零值比例: {analysis['zero_ratio']:.1f}%\n"
                    f"• 显示范围: 0-{vmax_best:.2f}\n"
                    f"• 隐藏极值: {analysis['max_val']-vmax_best:.2f}")
        
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
                verticalalignment='top', fontsize=10,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    
    plt.axis('off')
    
    # 保存推荐方案图
    recommended_path = os.path.join(output_dir, '推荐方案_灯光强度可视化.png')
    plt.savefig(recommended_path, dpi=300, bbox_inches='tight')
    print(f'✓ 推荐方案图已保存: {recommended_path}')
    
    return analysis, percentages, thresholds

def main():
    """主函数"""
    print("开始运行独立的灯光强度分析...")
    
    setup_matplotlib()
    
    try:
        # 读取灯光数据
        light_path = r"D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif"
        print(f"读取灯光数据: {os.path.basename(light_path)}")
        
        with rasterio.open(light_path) as src:
            light_data = src.read(1)
            light_data[light_data < 0] = 0  # 处理负值
            transform = src.transform
            meta = src.meta
            
        print("✓ 数据读取成功!")
        
        # 创建增强可视化
        analysis, percentages, thresholds = create_enhanced_visualization(light_data)
        
        print("\n=== 分析总结 ===")
        print(f"✓ 处理了 {analysis['total_pixels']:,} 个像素的灯光数据")
        print(f"✓ 识别出 {100-analysis['zero_ratio']:.1f}% 的有效灯光区域")
        print(f"✓ 商业潜力分类: 高潜力 {percentages['high']:.1f}%, "
              f"中等潜力 {percentages['medium']:.1f}%, 低潜力 {percentages['low']:.1f}%")
        
        print(f"\n=== 生成的文件 ===")
        print("✓ 完整_灯光强度分析报告.png - 综合分析报告")
        print("✓ 推荐方案_灯光强度可视化.png - 最佳可视化方案")
        
        print(f"\n=== 关键发现 ===")
        if 'percentiles' in analysis:
            print(f"• 95%的数据集中在 0-{analysis['percentiles']['95']:.1f} 范围内")
            print(f"• 推荐使用95%截断方案，可显示 {100-5:.0f}% 的数据细节")
        print(f"• 高商业潜力区域占总面积的 {percentages['high']:.1f}%")
        print(f"• 建议重点关注灯光强度 ≥ {thresholds['high_threshold']:.1f} 的区域")
        
    except FileNotFoundError:
        print("错误: 未找到灯光数据文件")
    except Exception as e:
        print(f" 处理错误: {e}")

if __name__ == "__main__":
    main()
