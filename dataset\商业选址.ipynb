{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Page Tags:\n", "ImageWidth: 196\n", "ImageLength: 202\n", "BitsPerSample: 32\n", "Compression: 5\n", "PhotometricInterpretation: 1\n", "SamplesPerPixel: 1\n", "PlanarConfiguration: 1\n", "Predictor: 1\n", "TileWidth: 128\n", "TileLength: 128\n", "TileOffsets: (1165, 6203, 9846, 14245)\n", "TileByteCounts: (5038, 3643, 4399, 1543)\n", "SampleFormat: 2\n", "ModelPixelScaleTag: (1000.0, 1000.0, 0.0)\n", "ModelTiepointTag: (0.0, 0.0, 0.0, 860227.1434792285, 4484896.526255939, 0.0)\n", "GeoKeyDirectoryTag: (1, 1, 0, 24, 1024, 0, 1, 1, 1025, 0, 1, 1, 1026, 34737, 27, 0, 2048, 0, 1, 4326, 2049, 34737, 84, 27, 2050, 0, 1, 6326, 2051, 0, 1, 8901, 2054, 0, 1, 9102, 2055, 34736, 1, 6, 2056, 0, 1, 7030, 2057, 34736, 1, 7, 2059, 34736, 1, 8, 2061, 34736, 1, 9, 3072, 0, 1, 32767, 3073, 34737, 439, 111, 3074, 0, 1, 32767, 3075, 0, 1, 11, 3076, 0, 1, 9001, 3078, 34736, 1, 0, 3079, 34736, 1, 1, 3080, 34736, 1, 3, 3081, 34736, 1, 2, 3082, 34736, 1, 4, 3083, 34736, 1, 5)\n", "GeoDoubleParamsTag: (25.0, 47.0, 0.0, 105.0, 0.0, 0.0, 0.0174532925199433, 6378137.0, 298.257223563, 0.0)\n", "GeoAsciiParamsTag: PCS Name = WGS_1984_Albers|GCS Name = GCS_WGS_1984|Datum = D_WGS_1984|Ellipsoid = WGS_1984|Primem = Greenwich||ESRI PE String = PROJCS[\"WGS_1984_Albers\",GEOGCS[\"GCS_WGS_1984\",DATUM[\"D_WGS_1984\",SPHEROID[\"WGS_1984\",6378137.0,298.257223563]],PRIMEM[\"Greenwich\",0.0],UNIT[\"Degree\",0.0174532925199433]],PROJECTION[\"Albers\"],PARAMETER[\"false_easting\",0.0],PARAMETER[\"false_northing\",0.0],PARAMETER[\"central_meridian\",105.0],PARAMETER[\"standard_parallel_1\",25.0],PARAMETER[\"standard_parallel_2\",47.0],PARAMETER[\"latitude_of_origin\",0.0],UNIT[\"Meter\",1.0]]|\n", "GDAL_METADATA: <GDALMetadata>\n", "  <Item name=\"RepresentationType\" sample=\"0\">THEMATIC</Item>\n", "</GDALMetadata>\n", "GDAL_NODATA: -128\n", "\n", "\n", "Image Width: 196\n", "Full Metadata: None\n"]}], "source": ["import tifffile\n", "\n", "with tifffile.TiffFile(\"【立方数据学社】DMSP-like2024.tif\") as tif:\n", "    # 获取所有页面的标签（多页TIFF）\n", "    for page in tif.pages:\n", "        print(\"Page Tags:\")\n", "        for tag in page.tags:\n", "            tag_name = tag.name\n", "            tag_value = tag.value\n", "            print(f\"{tag_name}: {tag_value}\")\n", "        print(\"\\n\")\n", "\n", "    # 访问特定标签（示例：图像宽度）\n", "    first_page = tif.pages[0]\n", "    width = first_page.tags['ImageWidth'].value\n", "    print(f\"Image Width: {width}\")\n", "\n", "    # 查看所有元数据（字典形式）\n", "    metadata = tif.shaped_metadata  # 或 tif.imagej_metadata（ImageJ格式）\n", "    print(\"Full Metadata:\", metadata)"]}, {"cell_type": "code", "execution_count": null, "id": "13770077", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m运行具有“business_location (Python 3.9.23)”的单元格需要ipykernel包。\n", "\u001b[1;31m将“ipykernel”安装到 Python 环境中。\n", "\u001b[1;31m命令:“conda install -n business_location ipykernel --update-deps --force-reinstall”"]}], "source": ["import rasterio\n", "import numpy as np\n", "import pandas as pd\n", "import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "from rasterstats import zonal_stats\n", "from matplotlib.colors import LinearSegmentedColormap\n", "\n", "# ----------------------------\n", "# 1. 读取归一化后的灯光数据\n", "# ----------------------------\n", "try:\n", "    with rasterio.open('【立方数据学社】DMSP-like2024.tif') as src:\n", "        # 读取归一化灯光数据\n", "        normalized_lights = src.read(1)\n", "        \n", "        # 获取地理信息（用于坐标转换）\n", "        transform = src.transform\n", "        meta = src.meta\n", "        bounds = src.bounds\n", "except FileNotFoundError:\n", "    print(\"错误：未找到灯光数据文件，请确保【立方数据学社】DMSP-like2024.tif在当前目录中\")\n", "    # 退出程序或提供替代方案\n", "\n", "# ----------------------------\n", "# 2. 分类阈值设定（基于示例中的8/9≈0.888）\n", "# ----------------------------\n", "def classify_areas(data):\n", "    \"\"\"根据阈值对区域进行分类\"\"\"\n", "    # 定义分类阈值（可调整）\n", "    low_threshold = 0.5\n", "    high_threshold = 1.0\n", "    \n", "    # 创建分类矩阵\n", "    classified = np.zeros_like(data, dtype=np.int8)\n", "    classified[data < low_threshold] = 0      # 低潜力区\n", "    classified[(data >= low_threshold) & (data < high_threshold)] = 1  # 中等潜力区\n", "    classified[data >= high_threshold] = 2     # 高潜力区\n", "    \n", "    return classified\n", "\n", "# 执行分类\n", "classified_data = classify_areas(normalized_lights)\n", "\n", "# 计算各类别占比\n", "total_pixels = np.prod(normalized_lights.shape)\n", "low_percent = np.sum(classified_data == 0) / total_pixels * 100\n", "med_percent = np.sum(classified_data == 1) / total_pixels * 100\n", "high_percent = np.sum(classified_data == 2) / total_pixels * 100\n", "\n", "print(f\"区域潜力分类: 低({low_percent:.1f}%) 中({med_percent:.1f}%) 高({high_percent:.1f}%)\")\n", "\n", "# ----------------------------\n", "# 3. 空间聚合（聚合到行政区）\n", "# ----------------------------\n", "# 读取行政区划矢量数据（示例使用北京行政区）\n", "# 实际数据可从 https://gadm.org/download_country_v3.html 下载\n", "try:\n", "    districts = gpd.read_file(\"D:\\桌面\\比赛文件\\商业选址\\SAU\\SAU\\District\\BeijingDistrict_2022.shp\")\n", "except FileNotFoundError:\n", "    print(\"警告：未找到行政区划数据文件，将使用示例数据\")\n", "    # 这里可以提供一个简单的示例区域或退出\n", "\n", "# 计算每个行政区的平均灯光强度\n", "stats = zonal_stats(\n", "    vectors=districts,\n", "    raster=normalized_lights,\n", "    affine=transform,\n", "    stats=['mean'],\n", "    geojson_out=True\n", ")\n", "\n", "# 转换为GeoDataFrame并添加统计数据\n", "districts_gdf = gpd.GeoDataFrame.from_features(stats)\n", "districts_gdf.rename(columns={'mean': 'light_intensity'}, inplace=True)\n", "\n", "# 根据灯光强度分类\n", "districts_gdf['potential'] = pd.cut(\n", "    districts_gdf['light_intensity'],\n", "    bins=[-np.inf, 0.5, 1.0, np.inf],\n", "    labels=['低潜力', '中等潜力', '高潜力']\n", ")\n", "\n", "# ----------------------------\n", "# 4. 变化检测（需历史数据）\n", "# ----------------------------\n", "try:\n", "    # 尝试读取2023年数据\n", "    with rasterio.open(\"D:\\桌面\\【立方数据学社】北京市\\全省范围的数据\\【立方数据学社】北京市\\【立方数据学社】北京市\\【立方数据学社】DMSP-like2023.tif\") as src:\n", "        lights_2023 = src.read(1)\n", "    \n", "    # 计算变化率（假设已归一化）\n", "    growth_rate = (normalized_lights - lights_2023) / lights_2023 * 100\n", "    \n", "    # 标记显著增长区（>10%增长）\n", "    growth_mask = growth_rate > 10\n", "    \n", "except FileNotFoundError:\n", "    print(\"警告：未找到历史数据，跳过变化检测\")\n", "\n", "# ----------------------------\n", "# 5. 可视化热点地图\n", "# ----------------------------\n", "# 创建自定义颜色映射\n", "colors = ['#2b83ba', '#abdda4', '#fdae61', '#d7191c']  # 蓝-绿-橙-红\n", "cmap = LinearSegmentedColormap.from_list('potential', colors, N=4)\n", "\n", "# 绘制行政区潜力图\n", "fig, ax = plt.subplots(1, 2, figsize=(18, 8))\n", "\n", "# 子图1：原始灯光数据热力图\n", "im1 = ax[0].imshow(normalized_lights, cmap='viridis', vmin=0, vmax=2)\n", "districts_gdf.boundary.plot(ax=ax[0], edgecolor='white', linewidth=0.5)\n", "ax[0].set_title('归一化灯光强度 (2024)')\n", "fig.colorbar(im1, ax=ax[0], label='灯光强度')\n", "\n", "# 子图2：分类潜力区域\n", "districts_gdf.plot(\n", "    column='potential',\n", "    ax=ax[1],\n", "    legend=True,\n", "    cmap=cmap,\n", "    legend_kwds={'loc': 'lower right'},\n", "    edgecolor='black',\n", "    linewidth=0.3\n", ")\n", "\n", "# 添加增长区域标记（如果可用）\n", "if 'growth_mask' in locals():\n", "    # 将增长区域转换为多边形\n", "    from rasterio.features import shapes\n", "    growth_polys = [{'properties': {}, 'geometry': poly} \n", "                   for poly, _ in shapes(growth_mask.astype(np.uint8), \n", "                   transform=transform)]\n", "    growth_gdf = gpd.GeoDataFrame.from_features(growth_polys)\n", "    growth_gdf.plot(ax=ax[1], facecolor='none', edgecolor='yellow', linewidth=1.5)\n", "\n", "# 添加行政区名称\n", "for idx, row in districts_gdf.iterrows():\n", "    # 尝试获取NAME字段，如果不存在则使用索引\n", "    name = row.get('NAME', f'区域{idx}')\n", "    ax[1].annotate(\n", "        text=name,\n", "        xy=(row.geometry.centroid.x, row.geometry.centroid.y),\n", "        ha='center',\n", "        fontsize=8,\n", "        color='black',\n", "        bbox=dict(boxstyle=\"round,pad=0.1\", fc='white', alpha=0.7)\n", "    )\n", "\n", "ax[1].set_title('商业选址潜力分类')\n", "plt.tight_layout()\n", "\n", "# 保存结果\n", "plt.savefig('business_location_potential.png', dpi=300)\n", "try:\n", "    districts_gdf.to_file('business_potential_districts.shp')\n", "except Exception as e:\n", "    print(f\"保存矢量数据时出错: {e}\")\n", "print(\"可视化结果已保存为 business_location_potential.png\")"]}], "metadata": {"kernelspec": {"display_name": "business_location", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}