import tifffile

with tifffile.TiffFile("【立方数据学社】DMSP-like2024.tif") as tif:
    # 获取所有页面的标签（多页TIFF）
    for page in tif.pages:
        print("Page Tags:")
        for tag in page.tags:
            tag_name = tag.name
            tag_value = tag.value
            print(f"{tag_name}: {tag_value}")
        print("\n")

    # 访问特定标签（示例：图像宽度）
    first_page = tif.pages[0]
    width = first_page.tags['ImageWidth'].value
    print(f"Image Width: {width}")

    # 查看所有元数据（字典形式）
    metadata = tif.shaped_metadata  # 或 tif.imagej_metadata（ImageJ格式）
    print("Full Metadata:", metadata)

import rasterio
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
from rasterstats import zonal_stats
from matplotlib.colors import LinearSegmentedColormap

# ----------------------------
# 1. 读取归一化后的灯光数据
# ----------------------------
try:
    with rasterio.open('【立方数据学社】DMSP-like2024.tif') as src:
        # 读取归一化灯光数据
        normalized_lights = src.read(1)
        
        # 获取地理信息（用于坐标转换）
        transform = src.transform
        meta = src.meta
        bounds = src.bounds
except FileNotFoundError:
    print("错误：未找到灯光数据文件，请确保【立方数据学社】DMSP-like2024.tif在当前目录中")
    # 退出程序或提供替代方案

# ----------------------------
# 2. 分类阈值设定（基于示例中的8/9≈0.888）
# ----------------------------
def classify_areas(data):
    """根据阈值对区域进行分类"""
    # 定义分类阈值（可调整）
    low_threshold = 0.5
    high_threshold = 1.0
    
    # 创建分类矩阵
    classified = np.zeros_like(data, dtype=np.int8)
    classified[data < low_threshold] = 0      # 低潜力区
    classified[(data >= low_threshold) & (data < high_threshold)] = 1  # 中等潜力区
    classified[data >= high_threshold] = 2     # 高潜力区
    
    return classified

# 执行分类
classified_data = classify_areas(normalized_lights)

# 计算各类别占比
total_pixels = np.prod(normalized_lights.shape)
low_percent = np.sum(classified_data == 0) / total_pixels * 100
med_percent = np.sum(classified_data == 1) / total_pixels * 100
high_percent = np.sum(classified_data == 2) / total_pixels * 100

print(f"区域潜力分类: 低({low_percent:.1f}%) 中({med_percent:.1f}%) 高({high_percent:.1f}%)")

# ----------------------------
# 3. 空间聚合（聚合到行政区）
# ----------------------------
# 读取行政区划矢量数据（示例使用北京行政区）
# 实际数据可从 https://gadm.org/download_country_v3.html 下载
try:
    districts = gpd.read_file("D:\桌面\比赛文件\商业选址\SAU\SAU\District\BeijingDistrict_2022.shp")
except FileNotFoundError:
    print("警告：未找到行政区划数据文件，将使用示例数据")
    # 这里可以提供一个简单的示例区域或退出

# 计算每个行政区的平均灯光强度
stats = zonal_stats(
    vectors=districts,
    raster=normalized_lights,
    affine=transform,
    stats=['mean'],
    geojson_out=True
)

# 转换为GeoDataFrame并添加统计数据
districts_gdf = gpd.GeoDataFrame.from_features(stats)
districts_gdf.rename(columns={'mean': 'light_intensity'}, inplace=True)

# 根据灯光强度分类
districts_gdf['potential'] = pd.cut(
    districts_gdf['light_intensity'],
    bins=[-np.inf, 0.5, 1.0, np.inf],
    labels=['低潜力', '中等潜力', '高潜力']
)

# ----------------------------
# 4. 变化检测（需历史数据）
# ----------------------------
try:
    # 尝试读取2023年数据
    with rasterio.open("D:\桌面\【立方数据学社】北京市\全省范围的数据\【立方数据学社】北京市\【立方数据学社】北京市\【立方数据学社】DMSP-like2023.tif") as src:
        lights_2023 = src.read(1)
    
    # 计算变化率（假设已归一化）
    growth_rate = (normalized_lights - lights_2023) / lights_2023 * 100
    
    # 标记显著增长区（>10%增长）
    growth_mask = growth_rate > 10
    
except FileNotFoundError:
    print("警告：未找到历史数据，跳过变化检测")

# ----------------------------
# 5. 可视化热点地图
# ----------------------------
# 创建自定义颜色映射
colors = ['#2b83ba', '#abdda4', '#fdae61', '#d7191c']  # 蓝-绿-橙-红
cmap = LinearSegmentedColormap.from_list('potential', colors, N=4)

# 绘制行政区潜力图
fig, ax = plt.subplots(1, 2, figsize=(18, 8))

# 子图1：原始灯光数据热力图
im1 = ax[0].imshow(normalized_lights, cmap='viridis', vmin=0, vmax=2)
districts_gdf.boundary.plot(ax=ax[0], edgecolor='white', linewidth=0.5)
ax[0].set_title('归一化灯光强度 (2024)')
fig.colorbar(im1, ax=ax[0], label='灯光强度')

# 子图2：分类潜力区域
districts_gdf.plot(
    column='potential',
    ax=ax[1],
    legend=True,
    cmap=cmap,
    legend_kwds={'loc': 'lower right'},
    edgecolor='black',
    linewidth=0.3
)

# 添加增长区域标记（如果可用）
if 'growth_mask' in locals():
    # 将增长区域转换为多边形
    from rasterio.features import shapes
    growth_polys = [{'properties': {}, 'geometry': poly} 
                   for poly, _ in shapes(growth_mask.astype(np.uint8), 
                   transform=transform)]
    growth_gdf = gpd.GeoDataFrame.from_features(growth_polys)
    growth_gdf.plot(ax=ax[1], facecolor='none', edgecolor='yellow', linewidth=1.5)

# 添加行政区名称
for idx, row in districts_gdf.iterrows():
    # 尝试获取NAME字段，如果不存在则使用索引
    name = row.get('NAME', f'区域{idx}')
    ax[1].annotate(
        text=name,
        xy=(row.geometry.centroid.x, row.geometry.centroid.y),
        ha='center',
        fontsize=8,
        color='black',
        bbox=dict(boxstyle="round,pad=0.1", fc='white', alpha=0.7)
    )

ax[1].set_title('商业选址潜力分类')
plt.tight_layout()

# 保存结果
plt.savefig('business_location_potential.png', dpi=300)
try:
    districts_gdf.to_file('business_potential_districts.shp')
except Exception as e:
    print(f"保存矢量数据时出错: {e}")
print("可视化结果已保存为 business_location_potential.png")