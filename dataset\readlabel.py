import tifffile

with tifffile.TiffFile("D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif") as tif:
    # 获取所有页面的标签（多页TIFF）
    for page in tif.pages:
        print("Page Tags:")
        for tag in page.tags:
            tag_name = tag.name
            tag_value = tag.value
            print(f"{tag_name}: {tag_value}")
        print("\n")

    # 访问特定标签（示例：图像宽度）
    first_page = tif.pages[0]
    width = first_page.tags['ImageWidth'].value
    print(f"Image Width: {width}")

    # 查看所有元数据（字典形式）
    metadata = tif.shaped_metadata  # 或 tif.imagej_metadata（ImageJ格式）
    print("Full Metadata:", metadata)