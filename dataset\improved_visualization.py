#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的灯光强度可视化脚本
解决图像归一化灯光强度无法显示完全的问题
"""

import numpy as np
import pandas as pd
import geopandas as gpd
import rasterio
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import os
from matplotlib.colors import LinearSegmentedColormap, LogNorm, PowerNorm
from matplotlib import font_manager
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体支持"""
    chinese_font_names = ['SimHei', 'WenQuanYi Micro Hei', 'Heiti TC', 'Microsoft YaHei', 'Arial Unicode MS']
    available_fonts = font_manager.findSystemFonts()
    
    for font_name in chinese_font_names:
        for font_path in available_fonts:
            if font_name.lower() in font_path.lower():
                plt.rcParams["font.family"] = [font_name]
                print(f"已设置中文字体: {font_name}")
                return
    
    print("警告: 未找到指定的中文字体，将使用系统默认字体")
    plt.rcParams['axes.unicode_minus'] = False

def analyze_data_distribution(data):
    """分析数据分布，为可视化提供参数建议"""
    # 排除零值和负值
    valid_data = data[data > 0]
    
    if len(valid_data) == 0:
        return {
            'min_val': 0,
            'max_val': 1,
            'mean_val': 0,
            'percentile_95': 1,
            'percentile_99': 1,
            'zero_ratio': 100,
            'dynamic_range': 1,
            'suggested_method': 'linear'
        }
    
    stats = {
        'min_val': np.min(valid_data),
        'max_val': np.max(data),
        'mean_val': np.mean(valid_data),
        'percentile_95': np.percentile(valid_data, 95),
        'percentile_99': np.percentile(valid_data, 99),
        'zero_ratio': (np.sum(data == 0) / data.size) * 100,
        'dynamic_range': np.max(data) / np.min(valid_data) if np.min(valid_data) > 0 else 1
    }
    
    # 根据数据特征建议可视化方法
    if stats['dynamic_range'] > 1000:
        stats['suggested_method'] = 'log'
    elif stats['dynamic_range'] > 100:
        stats['suggested_method'] = 'power'
    else:
        stats['suggested_method'] = 'linear'
    
    return stats

def create_improved_visualization(light_data, transform, output_dir='output_images'):
    """创建改进的灯光强度可视化"""
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 分析数据分布
    stats = analyze_data_distribution(light_data)
    
    print("=== 数据分布分析 ===")
    print(f"有效数据范围: {stats['min_val']:.4f} - {stats['max_val']:.4f}")
    print(f"平均值: {stats['mean_val']:.4f}")
    print(f"95%分位数: {stats['percentile_95']:.4f}")
    print(f"99%分位数: {stats['percentile_99']:.4f}")
    print(f"零值比例: {stats['zero_ratio']:.1f}%")
    print(f"动态范围: {stats['dynamic_range']:.1f}倍")
    print(f"建议方法: {stats['suggested_method']}")
    
    # 创建多种可视化方案
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('灯光强度可视化对比 - 多种方案', fontsize=16, fontweight='bold')
    
    # 方案1: 线性刻度，全范围
    im1 = axes[0, 0].imshow(
        light_data,
        cmap='plasma',
        vmin=0,
        vmax=stats['max_val'],
        interpolation='bilinear'
    )
    axes[0, 0].set_title('方案1: 线性刻度 (全范围)')
    plt.colorbar(im1, ax=axes[0, 0], label='灯光强度')
    
    # 方案2: 线性刻度，95%分位数截断
    im2 = axes[0, 1].imshow(
        light_data,
        cmap='plasma',
        vmin=0,
        vmax=stats['percentile_95'],
        interpolation='bilinear'
    )
    axes[0, 1].set_title(f'方案2: 线性刻度 (95%截断: {stats["percentile_95"]:.2f})')
    plt.colorbar(im2, ax=axes[0, 1], label='灯光强度')
    
    # 方案3: 对数刻度
    if stats['min_val'] > 0:
        log_vmin = max(stats['min_val'] * 0.1, 0.001)
        im3 = axes[0, 2].imshow(
            light_data,
            cmap='plasma',
            norm=LogNorm(vmin=log_vmin, vmax=stats['max_val']),
            interpolation='bilinear'
        )
        axes[0, 2].set_title(f'方案3: 对数刻度 ({log_vmin:.3f}-{stats["max_val"]:.2f})')
        plt.colorbar(im3, ax=axes[0, 2], label='灯光强度 (对数)')
    else:
        axes[0, 2].text(0.5, 0.5, '数据不适合对数刻度', ha='center', va='center', transform=axes[0, 2].transAxes)
        axes[0, 2].set_title('方案3: 对数刻度 (不适用)')
    
    # 方案4: 幂律刻度
    im4 = axes[1, 0].imshow(
        light_data,
        cmap='plasma',
        norm=PowerNorm(gamma=0.5, vmin=0, vmax=stats['max_val']),
        interpolation='bilinear'
    )
    axes[1, 0].set_title('方案4: 幂律刻度 (γ=0.5)')
    plt.colorbar(im4, ax=axes[1, 0], label='灯光强度 (幂律)')
    
    # 方案5: 自适应分段线性
    # 创建分段颜色映射
    breakpoints = [0, stats['percentile_95']*0.1, stats['percentile_95']*0.5, stats['percentile_95']]
    colors = ['#000033', '#000080', '#0080FF', '#FFFF00', '#FF8000', '#FF0000']
    n_bins = 256
    cmap_adaptive = LinearSegmentedColormap.from_list('adaptive', colors, N=n_bins)
    
    im5 = axes[1, 1].imshow(
        light_data,
        cmap=cmap_adaptive,
        vmin=0,
        vmax=stats['percentile_95'],
        interpolation='bilinear'
    )
    axes[1, 1].set_title('方案5: 自适应分段线性')
    plt.colorbar(im5, ax=axes[1, 1], label='灯光强度')
    
    # 方案6: 推荐方案（基于数据特征自动选择）
    if stats['suggested_method'] == 'log' and stats['min_val'] > 0:
        log_vmin = max(stats['min_val'] * 0.1, 0.001)
        im6 = axes[1, 2].imshow(
            light_data,
            cmap='plasma',
            norm=LogNorm(vmin=log_vmin, vmax=stats['max_val']),
            interpolation='bilinear'
        )
        title = f'方案6: 推荐-对数刻度'
        label = '灯光强度 (对数)'
    elif stats['suggested_method'] == 'power':
        im6 = axes[1, 2].imshow(
            light_data,
            cmap='plasma',
            norm=PowerNorm(gamma=0.5, vmin=0, vmax=stats['max_val']),
            interpolation='bilinear'
        )
        title = f'方案6: 推荐-幂律刻度'
        label = '灯光强度 (幂律)'
    else:
        im6 = axes[1, 2].imshow(
            light_data,
            cmap='plasma',
            vmin=0,
            vmax=stats['percentile_95'],
            interpolation='bilinear'
        )
        title = f'方案6: 推荐-线性刻度(95%截断)'
        label = '灯光强度'
    
    axes[1, 2].set_title(title)
    plt.colorbar(im6, ax=axes[1, 2], label=label)
    
    # 移除坐标轴
    for ax in axes.flat:
        ax.set_xticks([])
        ax.set_yticks([])
    
    plt.tight_layout()
    
    # 保存对比图
    comparison_path = os.path.join(output_dir, '灯光强度可视化对比.png')
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    print(f'可视化对比图已保存至: {comparison_path}')
    
    # 创建单独的推荐方案图
    plt.figure(figsize=(12, 10))
    
    if stats['suggested_method'] == 'log' and stats['min_val'] > 0:
        log_vmin = max(stats['min_val'] * 0.1, 0.001)
        im_final = plt.imshow(
            light_data,
            cmap='plasma',
            norm=LogNorm(vmin=log_vmin, vmax=stats['max_val']),
            interpolation='bilinear'
        )
        plt.colorbar(im_final, label='灯光强度 (对数刻度)', shrink=0.8)
        plt.title(f'推荐方案: 对数刻度可视化\n数据范围: {log_vmin:.3f} - {stats["max_val"]:.2f}', fontsize=14)
    elif stats['suggested_method'] == 'power':
        im_final = plt.imshow(
            light_data,
            cmap='plasma',
            norm=PowerNorm(gamma=0.5, vmin=0, vmax=stats['max_val']),
            interpolation='bilinear'
        )
        plt.colorbar(im_final, label='灯光强度 (幂律刻度)', shrink=0.8)
        plt.title(f'推荐方案: 幂律刻度可视化 (γ=0.5)\n数据范围: 0 - {stats["max_val"]:.2f}', fontsize=14)
    else:
        im_final = plt.imshow(
            light_data,
            cmap='plasma',
            vmin=0,
            vmax=stats['percentile_95'],
            interpolation='bilinear'
        )
        plt.colorbar(im_final, label='灯光强度', shrink=0.8)
        plt.title(f'推荐方案: 线性刻度可视化 (95%截断)\n数据范围: 0 - {stats["percentile_95"]:.2f}', fontsize=14)
    
    plt.axis('off')
    
    # 保存推荐方案图
    recommended_path = os.path.join(output_dir, '推荐_灯光强度可视化.png')
    plt.savefig(recommended_path, dpi=300, bbox_inches='tight')
    print(f'推荐可视化方案已保存至: {recommended_path}')
    
    return stats

def main():
    """主函数"""
    setup_chinese_font()
    
    # 读取灯光数据
    try:
        light_path = r"D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif"
        with rasterio.open(light_path) as src:
            normalized_lights = src.read(1)
            normalized_lights[normalized_lights < 0] = 0  # 处理负值
            transform = src.transform
            
        print("灯光数据读取成功")
        
        # 创建改进的可视化
        stats = create_improved_visualization(normalized_lights, transform)
        
        print("\n=== 可视化建议 ===")
        if stats['suggested_method'] == 'log':
            print("建议使用对数刻度，因为数据动态范围很大")
        elif stats['suggested_method'] == 'power':
            print("建议使用幂律刻度，可以增强中等强度区域的对比度")
        else:
            print("建议使用线性刻度配合95%分位数截断，可以避免极值影响")
            
    except FileNotFoundError:
        print("错误：未找到灯光数据文件")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
