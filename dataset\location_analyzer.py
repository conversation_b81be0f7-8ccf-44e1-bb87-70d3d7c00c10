# 导入必要的库
import numpy as np
import pandas as pd
import geopandas as gpd
import rasterio
from rasterstats import zonal_stats
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import os
from matplotlib.colors import LinearSegmentedColormap
from matplotlib import font_manager
# -*- coding: utf-8 -*- 
import sys
# 设置标准输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# ----------------------------
# 1. 读取灯光数据
# ----------------------------
try:
    with rasterio.open(r'D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif') as src:
        # 读取归一化灯光数据
        normalized_lights = src.read(1)
        
        # 处理负值（将负值设置为0）
        normalized_lights[normalized_lights < 0] = 0
        
        # 获取地理信息（用于坐标转换）
        transform = src.transform
        meta = src.meta
        bounds = src.bounds
        # 计算基本统计信息
        max_val = np.max(normalized_lights)
        min_val = np.min(normalized_lights)
        mean_val = np.mean(normalized_lights)
        
        non_zero_ratio = np.count_nonzero(normalized_lights) / normalized_lights.size * 100
        
        print(f"灯光数据处理完成: 最大值={max_val:.2f}, 最小值={min_val:.2f}, 平均值={mean_val:.2f}, 非零像素={non_zero_ratio:.1f}%")
except FileNotFoundError:
    print("错误：未找到灯光数据文件，请确保【立方数据学社】DMSP-like2024.tif在当前目录中")
    # 退出程序或提供替代方案

# ----------------------------
# 2. 分类阈值设定
# ----------------------------
def classify_areas(data):
    """根据阈值对区域进行分类"""
    # 定义分类阈值（可调整）
    low_threshold = 10
    high_threshold = 60
    print(f"分类阈值: 低潜力区 < {low_threshold:.2f}, 中等潜力区 < {high_threshold:.2f}, 高潜力区 >= {high_threshold:.2f}")
    # 创建分类矩阵
                        
    classified = np.zeros_like(data, dtype=np.int8)
    classified[data == 0] = 0  # 无灯光区
    classified[(data > 0)&(data < low_threshold)] = 1      # 低潜力区
    classified[(data >= low_threshold) & (data < high_threshold)] = 2  # 中等潜力区
    classified[data >= high_threshold] = 3     # 高潜力区
    
    return classified, low_threshold, high_threshold

# 执行分类
classified_data, low_threshold, high_threshold = classify_areas(normalized_lights)

# 计算各类别占比
total_pixels = np.prod(normalized_lights.shape)
no_light = np.sum(classified_data == 0) / total_pixels * 100
low_percent = np.sum(classified_data == 1) / total_pixels * 100
med_percent = np.sum(classified_data == 2) / total_pixels * 100
high_percent = np.sum(classified_data == 3) / total_pixels * 100

print(f"区域潜力分类: 无灯光({no_light:.1f}%) 低({low_percent:.1f}%) 中({med_percent:.1f}%) 高({high_percent:.1f}%)")
    

# 执行分类
classified_data = classify_areas(normalized_lights)

# 计算各类别占比
total_pixels = np.prod(normalized_lights.shape)
low_percent = np.sum(classified_data == 0) / total_pixels * 100
med_percent = np.sum(classified_data == 1) / total_pixels * 100
high_percent = np.sum(classified_data == 2) / total_pixels * 100

print(f"区域潜力分类: 低({low_percent:.1f}%) 中({med_percent:.1f}%) 高({high_percent:.1f}%)")

# ----------------------------
# 3. 空间聚合（聚合到行政区）
# ----------------------------
# 读取行政区划矢量数据（示例使用北京行政区）
try:
    districts = gpd.read_file(r"D:\桌面\比赛文件\商业选址\SAU\SAU\Grid\BeijingGrid_1km_3857.shp")
    print("行政区划数据CRS:", districts.crs)
    print("行政区划数据范围:", districts.total_bounds)
    print("灯光数据范围:", bounds)
    
    # 设置行政区划数据的CRS（假设为WGS84）
    if districts.crs is None:
        print("设置行政区划数据的CRS为WGS84 (EPSG:4326)...")
        districts = districts.set_crs(epsg=4326)
        print("设置后的行政区划数据CRS:", districts.crs)
    
    # 检查坐标参考系统是否匹配
    if districts.crs != meta['crs']:
        print("警告：行政区划数据和灯光数据的CRS不匹配！")
        print("尝试转换行政区划数据到灯光数据的CRS...")
        districts = districts.to_crs(meta['crs'])
        print("转换后的行政区划数据CRS:", districts.crs)

except (FileNotFoundError, ValueError, Exception) as e:
    print(f"警告：无法读取行政区划数据文件: {e}")
    print("将创建基于网格的虚拟行政区划...")

    # 创建基于网格的虚拟行政区划
    from shapely.geometry import Polygon

    # 根据灯光数据的形状创建网格
    height, width = normalized_lights.shape

    # 创建1km网格 (假设每个像素代表约500m)
    grid_size = 4  # 4个像素 = 约2km网格

    geometries = []
    grid_ids = []
    grid_names = []

    grid_count = 0
    for i in range(0, height, grid_size):
        for j in range(0, width, grid_size):
            # 创建网格多边形 (使用像素坐标)
            x1, y1 = j, i
            x2, y2 = min(j + grid_size, width), min(i + grid_size, height)

            # 创建矩形几何
            polygon = Polygon([(x1, y1), (x2, y1), (x2, y2), (x1, y2)])
            geometries.append(polygon)
            grid_ids.append(f"Grid_{i//grid_size}_{j//grid_size}")
            grid_names.append(f'网格区域_{grid_count+1}')
            grid_count += 1

    # 创建GeoDataFrame
    districts = gpd.GeoDataFrame({
        'ID': grid_ids,
        'NAME': grid_names
    }, geometry=geometries)

    # 设置CRS为像素坐标系统
    districts = districts.set_crs(epsg=4326)  # 临时设置，实际使用像素坐标

    print(f"创建了 {len(districts)} 个网格区域 ({grid_size}x{grid_size} 像素网格)")
    print("注意：使用像素坐标系统进行分析")

# 配置matplotlib使用Agg后端
plt.switch_backend('Agg')  # 使用Agg后端避免显示问题

# 尝试查找并设置中文字体
def set_chinese_font():
    # 中文字体名称列表
    chinese_font_names = ['SimHei', 'WenQuanYi Micro Hei', 'Heiti TC', 'Microsoft YaHei', 'Arial Unicode MS']
    
    # 查找系统中可用的中文字体
    available_fonts = font_manager.findSystemFonts()
    
    for font_name in chinese_font_names:
        for font_path in available_fonts:
            if font_name.lower() in font_path.lower():
                plt.rcParams["font.family"] = [font_name]
                print(f"已设置中文字体: {font_name} ({font_path})")
                return
    
    # 如果没有找到指定的中文字体，尝试使用系统默认字体
    print("警告: 未找到指定的中文字体，将使用系统默认字体。中文可能无法正常显示。")
    print("请安装中文字体如SimHei、Microsoft YaHei等以解决此问题。")

# 设置中文字体
set_chinese_font()
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题


# 计算每个行政区的平均灯光强度
print("开始计算行政区平均灯光强度...")
print("灯光数据形状:", normalized_lights.shape)
print("灯光数据非零值比例:", np.count_nonzero(normalized_lights)/np.prod(normalized_lights.shape)*100, "%")
print("行政区划数量:", len(districts))

# 检查是否使用了网格替代方案
if 'Grid_' in districts['ID'].iloc[0]:
    print("使用网格坐标系统，采用直接计算方法...")

    # 直接计算网格区域的灯光强度
    stats_data = []
    for idx, row in districts.iterrows():
        geom = row.geometry
        bounds = geom.bounds  # (minx, miny, maxx, maxy)

        # 转换为整数像素坐标
        x1, y1, x2, y2 = map(int, bounds)

        # 确保坐标在有效范围内
        x1 = max(0, x1)
        y1 = max(0, y1)
        x2 = min(normalized_lights.shape[1], x2)
        y2 = min(normalized_lights.shape[0], y2)

        # 提取区域数据
        if x2 > x1 and y2 > y1:
            region_data = normalized_lights[y1:y2, x1:x2]
            mean_intensity = np.mean(region_data) if region_data.size > 0 else 0
        else:
            mean_intensity = 0

        stats_data.append({
            'properties': {
                'mean': mean_intensity,
                'ID': row['ID'],
                'NAME': row['NAME']
            },
            'geometry': row.geometry
        })

    stats = stats_data
    print(f"网格计算完成，处理了 {len(stats)} 个网格区域")

else:
    # 使用传统的zonal_stats方法
    try:
        stats = zonal_stats(
            vectors=districts,
            raster=normalized_lights,
            affine=transform,
            stats=['mean'],
            geojson_out=True,
            nodata=0
        )
        print("zonal_stats计算完成")
    except Exception as e:
        print(f"zonal_stats计算失败: {e}")
        print("使用简化的网格计算方法...")

        # 简化的网格计算
        stats_data = []
        height, width = normalized_lights.shape
        grid_size = 4

        for i, row in districts.iterrows():
            # 简单的网格平均计算
            grid_row = i // (width // grid_size)
            grid_col = i % (width // grid_size)

            y1 = grid_row * grid_size
            y2 = min(y1 + grid_size, height)
            x1 = grid_col * grid_size
            x2 = min(x1 + grid_size, width)

            if y2 > y1 and x2 > x1:
                region_data = normalized_lights[y1:y2, x1:x2]
                mean_intensity = np.mean(region_data)
            else:
                mean_intensity = 0

            stats_data.append({
                'properties': {
                    'mean': mean_intensity,
                    'ID': row.get('ID', f'Grid_{i}'),
                    'NAME': row.get('NAME', f'区域_{i+1}')
                },
                'geometry': row.geometry
            })

        stats = stats_data

# 添加调试信息
print("统计结果数量:", len(stats))
if len(stats) > 0:
    valid_means = [stat['properties']['mean'] for stat in stats if stat['properties']['mean'] is not None]
    if valid_means:
        print(f"前5个区域的平均强度: {valid_means[:5]}")
        print(f"平均强度范围: {min(valid_means):.3f} - {max(valid_means):.3f}")
    else:
        print("警告: 没有有效的统计结果")

# 转换为GeoDataFrame并添加统计数据
districts_gdf = gpd.GeoDataFrame.from_features(stats)
districts_gdf.rename(columns={'mean': 'light_intensity'}, inplace=True)

# 处理缺失值
districts_gdf['light_intensity'] = districts_gdf['light_intensity'].fillna(0)

# 打印灯光强度列
print("灯光强度列数据:")
print(districts_gdf['light_intensity'])

# 根据灯光强度分类
districts_gdf['potential'] = pd.cut(
    districts_gdf['light_intensity'],
    bins=[-np.inf,0, 10, 60, np.inf],
    labels=['无灯光','低潜力', '中等潜力', '高潜力']
).astype(str)  # 转换为字符串类型

# ----------------------------
# 4. 变化检测（需历史数据）
# ----------------------------
try:
    # 尝试读取2023年数据
    with rasterio.open("D:\桌面\【立方数据学社】北京市\全省范围的数据\【立方数据学社】北京市\【立方数据学社】北京市\【立方数据学社】DMSP-like2023.tif") as src:
        lights_2023 = src.read(1)
        
    # 计算变化率（假设已归一化）
    # 处理除零情况
    with np.errstate(divide='ignore', invalid='ignore'):
        growth_rate = (normalized_lights - lights_2023) / lights_2023 * 100
        #*100是为了将变化率改为百分比
        growth_rate[np.isinf(growth_rate)] = 0  # 处理无穷大值
        growth_rate[np.isnan(growth_rate)] = 0  # 处理NaN值
    # 标记显著增长区（>10%增长）
    growth_mask = growth_rate > 10
    # 标记负增长区域
    # decline_mask = growth_rate < 0
except FileNotFoundError:
    print("警告：未找到历史数据，跳过变化检测")

# ----------------------------
# 5. 可视化热点地图
# ----------------------------
# 创建自定义颜色映射
colors = ['#2b83ba', '#abdda4', '#fdae61', '#d7191c']  # 蓝-绿-橙-红
cmap = LinearSegmentedColormap.from_list('potential', colors, N=4)

# 绘制行政区潜力图
fig, ax = plt.subplots(1, 2, figsize=(18, 8))

# 子图1：原始灯光数据热力图
# 添加灯光数据统计信息
print('灯光数据统计信息:')
print(f'最大值: {np.max(normalized_lights)}')
print(f'最小值: {np.min(normalized_lights)}')
print(f'平均值: {np.mean(normalized_lights)}')
print(f'非零像素比例: {np.count_nonzero(normalized_lights)/np.prod(normalized_lights.shape)*100}%')

# 调整可视化参数，确保数据正确显示
from matplotlib.colors import LogNorm

# 计算数据的有效范围，排除零值
non_zero_data = normalized_lights[normalized_lights > 0]
if len(non_zero_data) > 0:
    data_min = np.min(non_zero_data)
    data_max = np.max(normalized_lights)
    data_95th = np.percentile(non_zero_data, 95)

    print(f"数据范围: 最小值(非零)={data_min:.4f}, 最大值={data_max:.4f}, 95%分位数={data_95th:.4f}")

    # 方案1: 使用线性刻度，但限制上限为95%分位数以增强对比度
    im1 = ax[0].imshow(
        normalized_lights,
        cmap='plasma',  # 使用plasma颜色映射，对灯光数据更友好
        vmin=0,
        vmax=data_95th,  # 使用95%分位数作为上限，避免极值影响显示
        interpolation='bilinear'  # 双线性插值使图像更平滑
    )
    colorbar_label = f'灯光强度 (上限: {data_95th:.2f})'

    # 如果数据范围很大，可以选择使用对数刻度
    if data_max / data_min > 100:  # 如果数据跨度超过100倍，使用对数刻度
        # 使用稍微调整的最小值避免对数刻度问题
        log_vmin = max(data_min * 0.1, 0.001)
        im1 = ax[0].imshow(
            normalized_lights,
            cmap='plasma',
            norm=LogNorm(vmin=log_vmin, vmax=data_max),
            interpolation='bilinear'
        )
        colorbar_label = f'灯光强度 (对数刻度, 范围: {log_vmin:.3f}-{data_max:.2f})'
else:
    # 如果没有非零数据，使用简单的线性刻度
    im1 = ax[0].imshow(
        normalized_lights,
        cmap='plasma',
        vmin=0,
        vmax=1,
        interpolation='bilinear'
    )
    colorbar_label = '灯光强度'

# districts_gdf.boundary.plot(ax=ax[0], edgecolor='white', linewidth=1.0)
ax[0].set_title('归一化灯光强度 (2024)')
fig.colorbar(im1, ax=ax[0], label=colorbar_label)

# 子图2：分类潜力区域
districts_gdf.plot(
    column='potential',
    ax=ax[1],
    legend=True,
    cmap=cmap,
    legend_kwds={'loc': 'lower right'},
    edgecolor='black',
    linewidth=0.3
)

# 添加增长区域标记（如果可用）
if 'growth_mask' in locals():
    # 将增长区域转换为多边形
    from rasterio.features import shapes
    growth_polys = [{'properties': {}, 'geometry': poly} 
                   for poly, _ in shapes(growth_mask.astype(np.uint8), 
                   transform=transform)]
    growth_gdf = gpd.GeoDataFrame.from_features(growth_polys)
    growth_gdf.plot(ax=ax[1], facecolor='none', edgecolor='yellow', linewidth=1.5)
# if 'decline_mask' in locals():
#     # 将增长区域转换为多边形
#     from rasterio.features import shapes
#     decline_polys = [{'properties': {}, 'geometry': poly} 
#                    for poly, _ in shapes(decline_mask.astype(np.uint8), 
#                    transform=transform)]
#     decline_gdf = gpd.GeoDataFrame.from_features(decline_polys)
#     decline_gdf.plot(ax=ax[1], facecolor='none', edgecolor='red', linewidth=1.5)
# 添加行政区名称
# for idx, row in districts_gdf.iterrows():
#     # 尝试获取NAME字段，如果不存在则使用索引
#     name = row.get('NAME', f'区域{idx}')
#     ax[1].annotate(
#         text=name,
#         xy=(row.geometry.centroid.x, row.geometry.centroid.y),
#         ha='center',
#         fontsize=8,
#         color='black',
#         bbox=dict(boxstyle="round,pad=0.1", fc='white', alpha=0.7)
#     )

ax[1].set_title('商业选址潜力分类')
plt.tight_layout()

# 保存图片到文件
output_dir = 'd:\\桌面\\商业选址1\\output_images'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 保存潜力地图
potential_map_path = os.path.join(output_dir, '商业潜力地图1km.png')
plt.savefig(potential_map_path, dpi=300, bbox_inches='tight')
print(f'潜力地图已保存至: {potential_map_path}')

# 查看数据框列名，确保使用正确的行政区名称列
print('数据框列名:', districts_gdf.columns.tolist())

# 创建灯光强度柱状图
fig2, ax2 = plt.subplots(figsize=(12, 8))

try:
    # 尝试使用可能的行政区名称列
    name_column = None
    for col in ['NAME', 'district', '行政区', 'name', '区名']:
        if col in districts_gdf.columns:
            name_column = col
            break
    
    if name_column:
        districts_gdf.sort_values('light_intensity', ascending=False).plot(
            x=name_column,
            y='light_intensity',
            kind='bar',
            ax=ax2,
            color='skyblue'
        )
    else:
        # 如果没有找到合适的名称列，使用索引
        districts_gdf.sort_values('light_intensity', ascending=False).plot(
            y='light_intensity',
            kind='bar',
            ax=ax2,
            color='skyblue'
        )
        print("警告: 未找到行政区名称列，使用索引作为x轴")
    
    ax2.set_title('各行政区灯光强度对比')
    ax2.set_xlabel('行政区')
    ax2.set_ylabel('灯光强度')
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存柱状图
    bar_chart_path = os.path.join(output_dir, '行政区灯光强度对比.png')
    plt.savefig(bar_chart_path, dpi=300, bbox_inches='tight')
    print(f'行政区灯光强度对比图已保存至: {bar_chart_path}')
except Exception as e:
    print(f'创建柱状图时出错: {e}')

districts_gdf = districts_gdf.rename(columns={'light_intensity': 'light_int'})

# 尝试保存shapefile，如果失败则保存为其他格式
try:
    districts_gdf.to_file('business_potential_districts.shp', engine='fiona')
    print("✓ 商业潜力数据已保存为shapefile: business_potential_districts.shp")
except Exception as e:
    print(f"保存shapefile失败: {e}")
    print("尝试保存为其他格式...")

    try:
        # 保存为GeoJSON格式
        districts_gdf.to_file('business_potential_districts.geojson', driver='GeoJSON')
        print("✓ 商业潜力数据已保存为GeoJSON: business_potential_districts.geojson")
    except Exception as e2:
        print(f"保存GeoJSON也失败: {e2}")

        # 保存为CSV格式（不包含几何信息）
        try:
            df_no_geom = districts_gdf.drop(columns=['geometry'])
            df_no_geom.to_csv('business_potential_districts.csv', index=False, encoding='utf-8-sig')
            print("✓ 商业潜力数据已保存为CSV: business_potential_districts.csv")
        except Exception as e3:
            print(f"保存CSV也失败: {e3}")

print("\n🎉 === 分析完成 ===")
print("✓ 灯光强度可视化问题已解决")
print("✓ 商业潜力分析已完成")
print("✓ 生成的图像文件保存在 output_images/ 目录")
print("\n💡 主要发现:")
print(f"• 共分析了 {len(districts_gdf)} 个区域")
print(f"• 高潜力区域数量: {len(districts_gdf[districts_gdf['potential'] == '高潜力'])}")
print(f"• 中等潜力区域数量: {len(districts_gdf[districts_gdf['potential'] == '中等潜力'])}")
print(f"• 低潜力区域数量: {len(districts_gdf[districts_gdf['potential'] == '低潜力'])}")

# 显示前10个最高潜力区域
top_regions = districts_gdf.nlargest(10, 'light_int')
if len(top_regions) > 0:
    print(f"\n🏆 前10个最高潜力区域:")
    for idx, row in top_regions.iterrows():
        print(f"  {row['NAME']}: 灯光强度 {row['light_int']:.2f} ({row['potential']})")
else:
    print("\n⚠ 未找到有效的高潜力区域")