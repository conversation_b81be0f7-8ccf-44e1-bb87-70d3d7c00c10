#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的灯光强度可视化运行脚本
避免GDAL问题，专注于可视化改进
"""

import numpy as np
import rasterio
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm, PowerNorm, LinearSegmentedColormap
import os
import warnings
warnings.filterwarnings('ignore')

def setup_matplotlib():
    """配置matplotlib"""
    plt.switch_backend('Agg')  # 使用Agg后端避免显示问题
    
    # 设置中文字体
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        print("✓ 中文字体设置成功")
    except:
        print("⚠ 警告: 中文字体设置失败")

def classify_areas(light_data):
    """根据灯光强度分类区域潜力"""
    # 计算分类阈值
    non_zero_data = light_data[light_data > 0]
    if len(non_zero_data) == 0:
        return np.zeros_like(light_data), 0, 0
    
    # 使用分位数确定阈值
    low_threshold = np.percentile(non_zero_data, 33)
    high_threshold = np.percentile(non_zero_data, 67)
    
    # 分类
    classified = np.zeros_like(light_data)
    classified[light_data == 0] = 0  # 无灯光
    classified[(light_data > 0) & (light_data < low_threshold)] = 1  # 低潜力
    classified[(light_data >= low_threshold) & (light_data < high_threshold)] = 2  # 中等潜力
    classified[light_data >= high_threshold] = 3  # 高潜力
    
    return classified, low_threshold, high_threshold

def create_comprehensive_visualization(light_data, output_dir='output_images'):
    """创建综合的灯光强度可视化"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 数据分析
    total_pixels = light_data.size
    zero_pixels = np.sum(light_data == 0)
    non_zero_data = light_data[light_data > 0]
    
    print("=== 灯光数据分析 ===")
    print(f"数据形状: {light_data.shape}")
    print(f"总像素数: {total_pixels:,}")
    print(f"零值像素: {zero_pixels:,} ({zero_pixels/total_pixels*100:.1f}%)")
    print(f"非零像素: {len(non_zero_data):,}")
    print(f"数值范围: {np.min(light_data):.2f} - {np.max(light_data):.2f}")
    
    if len(non_zero_data) > 0:
        print(f"非零值范围: {np.min(non_zero_data):.2f} - {np.max(non_zero_data):.2f}")
        print(f"非零值平均: {np.mean(non_zero_data):.2f}")
        percentile_95 = np.percentile(non_zero_data, 95)
        print(f"95%分位数: {percentile_95:.2f}")
    
    # 区域分类
    classified_data, low_threshold, high_threshold = classify_areas(light_data)
    
    # 计算各类别占比
    no_light = np.sum(classified_data == 0) / total_pixels * 100
    low_percent = np.sum(classified_data == 1) / total_pixels * 100
    med_percent = np.sum(classified_data == 2) / total_pixels * 100
    high_percent = np.sum(classified_data == 3) / total_pixels * 100
    
    print(f"\n=== 区域潜力分类 ===")
    print(f"分类阈值: 低潜力 < {low_threshold:.1f}, 中等潜力 < {high_threshold:.1f}")
    print(f"无灯光: {no_light:.1f}%")
    print(f"低潜力: {low_percent:.1f}%")
    print(f"中等潜力: {med_percent:.1f}%")
    print(f"高潜力: {high_percent:.1f}%")
    
    # 创建主要可视化图
    fig, axes = plt.subplots(1, 2, figsize=(18, 8))
    fig.suptitle('改进后的灯光强度可视化分析', fontsize=16, fontweight='bold')
    
    # 子图1: 改进的灯光强度热力图
    if len(non_zero_data) > 0:
        data_min = np.min(non_zero_data)
        data_max = np.max(light_data)
        data_95th = np.percentile(non_zero_data, 95)
        
        print(f"\n=== 可视化参数 ===")
        print(f"数据范围: 最小值(非零)={data_min:.4f}, 最大值={data_max:.4f}")
        print(f"95%分位数={data_95th:.4f}")
        
        # 使用95%分位数截断的线性刻度
        im1 = axes[0].imshow(
            light_data,
            cmap='plasma',
            vmin=0,
            vmax=data_95th,
            interpolation='bilinear'
        )
        colorbar_label = f'灯光强度 (上限: {data_95th:.2f})'
        
        # 如果数据范围很大，可以选择使用对数刻度
        if data_max / data_min > 100:
            log_vmin = max(data_min * 0.1, 0.001)
            im1 = axes[0].imshow(
                light_data,
                cmap='plasma',
                norm=LogNorm(vmin=log_vmin, vmax=data_max),
                interpolation='bilinear'
            )
            colorbar_label = f'灯光强度 (对数刻度)'
            print("使用对数刻度显示")
        else:
            print("使用线性刻度显示")
    else:
        im1 = axes[0].imshow(
            light_data,
            cmap='plasma',
            vmin=0,
            vmax=1,
            interpolation='bilinear'
        )
        colorbar_label = '灯光强度'
    
    axes[0].set_title('归一化灯光强度 (2024)')
    fig.colorbar(im1, ax=axes[0], label=colorbar_label)
    axes[0].set_xticks([])
    axes[0].set_yticks([])
    
    # 子图2: 分类潜力区域
    colors = ['#2b83ba', '#abdda4', '#fdae61', '#d7191c']  # 蓝-绿-橙-红
    cmap_potential = LinearSegmentedColormap.from_list('potential', colors, N=4)
    
    im2 = axes[1].imshow(
        classified_data,
        cmap=cmap_potential,
        vmin=0,
        vmax=3,
        interpolation='nearest'
    )
    
    axes[1].set_title('商业潜力分类')
    axes[1].set_xticks([])
    axes[1].set_yticks([])
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#2b83ba', label=f'无灯光 ({no_light:.1f}%)'),
        Patch(facecolor='#abdda4', label=f'低潜力 ({low_percent:.1f}%)'),
        Patch(facecolor='#fdae61', label=f'中等潜力 ({med_percent:.1f}%)'),
        Patch(facecolor='#d7191c', label=f'高潜力 ({high_percent:.1f}%)')
    ]
    axes[1].legend(handles=legend_elements, loc='lower right')
    
    # 添加分类信息文本
    class_info = (f"分类阈值:\n"
                  f"低潜力: < {low_threshold:.1f}\n"
                  f"中等潜力: {low_threshold:.1f} - {high_threshold:.1f}\n"
                  f"高潜力: ≥ {high_threshold:.1f}")
    axes[1].text(0.02, 0.98, class_info, 
                transform=axes[1].transAxes, 
                verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存主图
    main_path = os.path.join(output_dir, '改进后_灯光强度分析.png')
    plt.savefig(main_path, dpi=300, bbox_inches='tight')
    print(f'\n✓ 主要分析图已保存: {main_path}')
    
    # 创建详细的对比图
    plt.figure(figsize=(20, 12))
    
    # 创建2x3的子图布局
    gs = plt.GridSpec(2, 3, hspace=0.3, wspace=0.3)
    
    # 原始问题方案
    ax1 = plt.subplot(gs[0, 0])
    im1 = ax1.imshow(light_data, cmap='viridis', vmin=0, vmax=np.max(light_data))
    ax1.set_title('问题方案: 线性全范围\n(大部分细节被压缩)')
    plt.colorbar(im1, ax=ax1)
    ax1.set_xticks([])
    ax1.set_yticks([])
    
    # 95%截断方案
    ax2 = plt.subplot(gs[0, 1])
    if len(non_zero_data) > 0:
        vmax_95 = np.percentile(non_zero_data, 95)
        im2 = ax2.imshow(light_data, cmap='plasma', vmin=0, vmax=vmax_95)
        ax2.set_title(f'解决方案1: 95%截断\n(显示范围: 0-{vmax_95:.1f})')
    else:
        im2 = ax2.imshow(light_data, cmap='plasma', vmin=0, vmax=1)
        ax2.set_title('解决方案1: 95%截断')
    plt.colorbar(im2, ax=ax2)
    ax2.set_xticks([])
    ax2.set_yticks([])
    
    # 对数刻度方案
    ax3 = plt.subplot(gs[0, 2])
    if len(non_zero_data) > 0 and np.min(non_zero_data) > 0:
        log_vmin = max(np.min(non_zero_data) * 0.1, 0.01)
        data_log = light_data.copy()
        data_log[data_log <= 0] = log_vmin
        im3 = ax3.imshow(data_log, cmap='plasma', norm=LogNorm(vmin=log_vmin, vmax=np.max(light_data)))
        ax3.set_title('解决方案2: 对数刻度\n(增强低值区域)')
    else:
        im3 = ax3.imshow(light_data, cmap='plasma')
        ax3.set_title('解决方案2: 对数刻度\n(数据不适用)')
    plt.colorbar(im3, ax=ax3)
    ax3.set_xticks([])
    ax3.set_yticks([])
    
    # 幂律刻度方案
    ax4 = plt.subplot(gs[1, 0])
    im4 = ax4.imshow(light_data, cmap='plasma', norm=PowerNorm(gamma=0.5))
    ax4.set_title('解决方案3: 幂律刻度\n(γ=0.5, 增强中等值)')
    plt.colorbar(im4, ax=ax4)
    ax4.set_xticks([])
    ax4.set_yticks([])
    
    # 分类结果
    ax5 = plt.subplot(gs[1, 1])
    im5 = ax5.imshow(classified_data, cmap=cmap_potential, vmin=0, vmax=3)
    ax5.set_title('商业潜力分类\n(基于灯光强度)')
    plt.colorbar(im5, ax=ax5)
    ax5.set_xticks([])
    ax5.set_yticks([])
    
    # 统计信息
    ax6 = plt.subplot(gs[1, 2])
    ax6.axis('off')
    
    stats_text = f"""数据统计信息:
    
总像素数: {total_pixels:,}
零值比例: {zero_pixels/total_pixels*100:.1f}%
非零像素: {len(non_zero_data):,}

数值范围: {np.min(light_data):.1f} - {np.max(light_data):.1f}
"""
    
    if len(non_zero_data) > 0:
        stats_text += f"""非零范围: {np.min(non_zero_data):.1f} - {np.max(non_zero_data):.1f}
平均值: {np.mean(non_zero_data):.1f}
95%分位数: {np.percentile(non_zero_data, 95):.1f}

分类结果:
无灯光: {no_light:.1f}%
低潜力: {low_percent:.1f}%
中等潜力: {med_percent:.1f}%
高潜力: {high_percent:.1f}%"""
    
    ax6.text(0.1, 0.9, stats_text, transform=ax6.transAxes, 
            verticalalignment='top', fontsize=12,
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.suptitle('灯光强度可视化完整解决方案对比', fontsize=16, fontweight='bold')
    
    # 保存对比图
    comparison_path = os.path.join(output_dir, '完整_可视化解决方案对比.png')
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    print(f'✓ 完整对比图已保存: {comparison_path}')
    
    return {
        'total_pixels': total_pixels,
        'zero_ratio': zero_pixels/total_pixels*100,
        'classification': {
            'no_light': no_light,
            'low': low_percent,
            'medium': med_percent,
            'high': high_percent
        },
        'thresholds': {
            'low': low_threshold,
            'high': high_threshold
        }
    }

def main():
    """主函数"""
    print("🚀 开始运行改进的灯光强度可视化分析...")
    
    setup_matplotlib()
    
    # 读取灯光数据
    try:
        light_path = r"D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif"
        print(f"📂 正在读取灯光数据: {os.path.basename(light_path)}")
        
        with rasterio.open(light_path) as src:
            light_data = src.read(1)
            # 处理负值
            light_data[light_data < 0] = 0
            
        print("✓ 灯光数据读取成功!")
        
        # 创建可视化
        results = create_comprehensive_visualization(light_data)
        
        print("\n🎯 === 分析完成 ===")
        print(f"✓ 数据处理: {results['total_pixels']:,} 像素")
        print(f"✓ 零值比例: {results['zero_ratio']:.1f}%")
        print(f"✓ 潜力分类: 高({results['classification']['high']:.1f}%) "
              f"中({results['classification']['medium']:.1f}%) "
              f"低({results['classification']['low']:.1f}%)")
        print(f"✓ 图像已保存到 output_images/ 目录")
        
        print("\n💡 === 使用建议 ===")
        print("1. 查看 '改进后_灯光强度分析.png' 了解主要结果")
        print("2. 查看 '完整_可视化解决方案对比.png' 对比不同方案")
        print("3. 推荐使用95%分位数截断方案进行日常分析")
        
    except FileNotFoundError:
        print("❌ 错误: 未找到灯光数据文件")
        print("请确保文件路径正确")
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
