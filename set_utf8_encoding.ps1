# 设置PowerShell终端编码为UTF-8
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding

# 验证编码设置
Write-Host "当前终端编码: $([console]::OutputEncoding.BodyName)"
Write-Host "UTF-8编码已设置，现在应该可以正确显示汉字了。"

# 可选：设置永久配置
# 以下命令会将编码设置添加到PowerShell配置文件
# 如果配置文件不存在，会创建一个新的
$profilePath = $PROFILE.CurrentUserAllHosts
if (-not (Test-Path -Path $profilePath)) {
    New-Item -ItemType File -Path $profilePath -Force | Out-Null
    Write-Host "已创建PowerShell配置文件: $profilePath"
}

# 将编码设置添加到配置文件（如果尚未存在）
$setting = '$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding'
if (-not (Get-Content -Path $profilePath | Select-String -Pattern $setting)) {
    Add-Content -Path $profilePath -Value $setting
    Write-Host "已将UTF-8编码设置添加到PowerShell配置文件"
}