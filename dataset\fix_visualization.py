#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门解决灯光强度可视化问题的脚本
"""

import numpy as np
import rasterio
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm, PowerNorm
import os
import warnings
warnings.filterwarnings('ignore')

def setup_matplotlib():
    """配置matplotlib"""
    plt.switch_backend('Agg')  # 使用Agg后端避免显示问题
    
    # 设置中文字体
    try:
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        print("中文字体设置成功")
    except:
        print("警告: 中文字体设置失败")

def analyze_light_data(data):
    """分析灯光数据特征"""
    # 基本统计
    total_pixels = data.size
    zero_pixels = np.sum(data == 0)
    non_zero_data = data[data > 0]
    
    stats = {
        'total_pixels': total_pixels,
        'zero_pixels': zero_pixels,
        'zero_ratio': (zero_pixels / total_pixels) * 100,
        'non_zero_count': len(non_zero_data),
        'min_val': np.min(data),
        'max_val': np.max(data),
        'mean_val': np.mean(data),
        'std_val': np.std(data)
    }
    
    if len(non_zero_data) > 0:
        stats.update({
            'min_nonzero': np.min(non_zero_data),
            'max_nonzero': np.max(non_zero_data),
            'mean_nonzero': np.mean(non_zero_data),
            'percentile_50': np.percentile(non_zero_data, 50),
            'percentile_75': np.percentile(non_zero_data, 75),
            'percentile_90': np.percentile(non_zero_data, 90),
            'percentile_95': np.percentile(non_zero_data, 95),
            'percentile_99': np.percentile(non_zero_data, 99)
        })
    
    return stats

def create_visualization_solutions(data, output_dir='output_images'):
    """创建多种可视化解决方案"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 分析数据
    stats = analyze_light_data(data)
    
    print("=== 灯光数据分析结果 ===")
    print(f"数据形状: {data.shape}")
    print(f"总像素数: {stats['total_pixels']:,}")
    print(f"零值像素: {stats['zero_pixels']:,} ({stats['zero_ratio']:.1f}%)")
    print(f"非零像素: {stats['non_zero_count']:,}")
    print(f"数值范围: {stats['min_val']:.2f} - {stats['max_val']:.2f}")
    print(f"平均值: {stats['mean_val']:.2f}")
    
    if 'min_nonzero' in stats:
        print(f"非零值范围: {stats['min_nonzero']:.2f} - {stats['max_nonzero']:.2f}")
        print(f"非零值平均: {stats['mean_nonzero']:.2f}")
        print(f"分位数 - 50%: {stats['percentile_50']:.2f}, 75%: {stats['percentile_75']:.2f}")
        print(f"分位数 - 90%: {stats['percentile_90']:.2f}, 95%: {stats['percentile_95']:.2f}")
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('灯光强度可视化解决方案对比', fontsize=16, fontweight='bold')
    
    # 解决方案1: 原始线性刻度（问题方案）
    im1 = axes[0, 0].imshow(data, cmap='viridis', vmin=0, vmax=stats['max_val'])
    axes[0, 0].set_title(f'问题方案: 线性刻度 (0-{stats["max_val"]:.0f})')
    plt.colorbar(im1, ax=axes[0, 0], label='灯光强度')
    
    # 解决方案2: 95%分位数截断（推荐）
    if 'percentile_95' in stats:
        vmax_95 = stats['percentile_95']
        im2 = axes[0, 1].imshow(data, cmap='plasma', vmin=0, vmax=vmax_95)
        axes[0, 1].set_title(f'解决方案1: 95%截断 (0-{vmax_95:.1f})')
        plt.colorbar(im2, ax=axes[0, 1], label='灯光强度')
    else:
        axes[0, 1].text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=axes[0, 1].transAxes)
        axes[0, 1].set_title('解决方案1: 无效')
    
    # 解决方案3: 对数刻度
    if 'min_nonzero' in stats and stats['min_nonzero'] > 0:
        # 为对数刻度设置合适的最小值
        log_vmin = max(stats['min_nonzero'] * 0.1, 0.01)
        log_vmax = stats['max_val']
        
        # 创建对数刻度的数据副本，避免零值问题
        data_log = data.copy()
        data_log[data_log <= 0] = log_vmin
        
        im3 = axes[1, 0].imshow(data_log, cmap='plasma', norm=LogNorm(vmin=log_vmin, vmax=log_vmax))
        axes[1, 0].set_title(f'解决方案2: 对数刻度 ({log_vmin:.2f}-{log_vmax:.0f})')
        plt.colorbar(im3, ax=axes[1, 0], label='灯光强度 (对数)')
    else:
        axes[1, 0].text(0.5, 0.5, '数据不适合对数刻度', ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 0].set_title('解决方案2: 不适用')
    
    # 解决方案4: 幂律刻度
    im4 = axes[1, 1].imshow(data, cmap='plasma', norm=PowerNorm(gamma=0.5, vmin=0, vmax=stats['max_val']))
    axes[1, 1].set_title('解决方案3: 幂律刻度 (γ=0.5)')
    plt.colorbar(im4, ax=axes[1, 1], label='灯光强度 (幂律)')
    
    # 移除坐标轴
    for ax in axes.flat:
        ax.set_xticks([])
        ax.set_yticks([])
    
    plt.tight_layout()
    
    # 保存对比图
    comparison_path = os.path.join(output_dir, '灯光强度可视化解决方案.png')
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    print(f'\n可视化解决方案对比图已保存: {comparison_path}')
    
    # 创建最佳解决方案的单独图像
    plt.figure(figsize=(12, 10))
    
    if 'percentile_95' in stats:
        vmax_best = stats['percentile_95']
        im_best = plt.imshow(data, cmap='plasma', vmin=0, vmax=vmax_best, interpolation='bilinear')
        plt.colorbar(im_best, label='灯光强度', shrink=0.8)
        plt.title(f'最佳解决方案: 95%分位数截断可视化\n显示范围: 0 - {vmax_best:.2f} (隐藏了{100-95:.0f}%的极值)', fontsize=14)
        
        # 添加统计信息文本
        info_text = (f"数据统计:\n"
                    f"总像素: {stats['total_pixels']:,}\n"
                    f"零值比例: {stats['zero_ratio']:.1f}%\n"
                    f"显示范围: 0-{vmax_best:.2f}\n"
                    f"隐藏极值: {stats['max_val']-vmax_best:.2f}")
        
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    else:
        plt.text(0.5, 0.5, '无有效数据可显示', ha='center', va='center', transform=plt.gca().transAxes)
        plt.title('无法创建可视化')
    
    plt.axis('off')
    
    # 保存最佳方案图
    best_path = os.path.join(output_dir, '最佳_灯光强度可视化.png')
    plt.savefig(best_path, dpi=300, bbox_inches='tight')
    print(f'最佳解决方案图已保存: {best_path}')
    
    return stats

def main():
    """主函数"""
    setup_matplotlib()
    
    # 读取灯光数据
    try:
        light_path = r"D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif"
        print(f"正在读取灯光数据: {light_path}")
        
        with rasterio.open(light_path) as src:
            light_data = src.read(1)
            # 处理负值
            light_data[light_data < 0] = 0
            
        print("灯光数据读取成功!")
        
        # 创建可视化解决方案
        stats = create_visualization_solutions(light_data)
        
        print("\n=== 解决方案建议 ===")
        print("1. 推荐使用95%分位数截断方案，这样可以:")
        print("   - 保留大部分数据的细节")
        print("   - 避免极值影响整体显示效果")
        print("   - 增强中低强度区域的对比度")
        print("\n2. 如果需要显示全部数据范围，可以使用对数刻度或幂律刻度")
        print("\n3. 避免使用原始线性刻度，因为极值会压缩大部分数据的显示")
        
    except FileNotFoundError:
        print(f"错误: 未找到灯光数据文件")
        print("请确保文件路径正确")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
