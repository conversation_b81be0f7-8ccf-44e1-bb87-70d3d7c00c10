# 导入必要的库
import numpy as np
import pandas as pd
import geopandas as gpd
import rasterio
from rasterstats import zonal_stats
import matplotlib.pyplot as plt
import os
from matplotlib.colors import LinearSegmentedColormap
from matplotlib import font_manager
import sys
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=UserWarning, module="rasterio")
warnings.filterwarnings("ignore", category=UserWarning, module="geopandas")

# 设置标准输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# ----------------------------
# 1. 读取并处理灯光数据（应用转换公式）
# ----------------------------
try:
    # 使用原始字符串避免转义问题
    light_path = r"D:\桌面\商业选址1\dataset\【立方数据学社】DMSP-like2024.tif"
    with rasterio.open(light_path) as src:
        # 读取原始灯光数据
        raw_lights = src.read(1)
        
        # 处理负值（将负值设置为0）
        raw_lights[raw_lights < 0] = 0
        
        # 应用转换公式：原始值 × 4/3
        transformed_lights = raw_lights * (4 / 3)
        
        # 获取地理信息
        transform = src.transform
        meta = src.meta
        bounds = src.bounds
        
        # 计算基本统计信息
        max_val = np.max(transformed_lights)
        min_val = np.min(transformed_lights)
        mean_val = np.mean(transformed_lights)
        non_zero_ratio = np.count_nonzero(transformed_lights) / transformed_lights.size * 100
        
        print(f"灯光数据处理完成: 最大值={max_val:.2f}, 最小值={min_val:.2f}, 平均值={mean_val:.2f}, 非零像素={non_zero_ratio:.1f}%")
        
except FileNotFoundError:
    print(f"错误：未找到灯光数据文件: {light_path}")
    sys.exit(1)
except Exception as e:
    print(f"处理灯光数据时出错: {e}")
    sys.exit(1)

# ----------------------------
# 2. 分类阈值设定（基于转换后的数据）
# ----------------------------
def classify_areas(data):
    """根据阈值对区域进行分类"""
    # 使用用户指定的固定阈值
    low_threshold = 50
    high_threshold = 63
    
    print(f"分类阈值: 低潜力区 < {low_threshold:.2f}, 中等潜力区 < {high_threshold:.2f}, 高潜力区 >= {high_threshold:.2f}")
    
    # 创建分类矩阵
    classified = np.zeros_like(data, dtype=np.int8)
    classified[data == 0] = 0                      # 无灯光区
    classified[(data > 0) & (data < low_threshold)] = 1  # 低潜力区
    classified[(data >= low_threshold) & (data < high_threshold)] = 2  # 中等潜力区
    classified[data >= high_threshold] = 3          # 高潜力区
    
    return classified, low_threshold, high_threshold

# 执行分类
classified_data, low_threshold, high_threshold = classify_areas(transformed_lights)

# 计算各类别占比
total_pixels = np.prod(transformed_lights.shape)
no_light = np.sum(classified_data == 0) / total_pixels * 100
low_percent = np.sum(classified_data == 1) / total_pixels * 100
med_percent = np.sum(classified_data == 2) / total_pixels * 100
high_percent = np.sum(classified_data == 3) / total_pixels * 100

print(f"区域潜力分类: 无灯光({no_light:.1f}%) 低({low_percent:.1f}%) 中({med_percent:.1f}%) 高({high_percent:.1f}%)")

# ----------------------------
# 3. 空间聚合（聚合到行政区）
# ----------------------------
# 读取行政区划矢量数据
try:
    # 使用原始字符串
    district_path = r"D:\桌面\比赛文件\商业选址\SAU\SAU\District\BeijingDistrict_2022.shp"
    print(f"尝试加载行政区划数据: {district_path}")
    
    # 尝试不同引擎加载
    try:
        districts = gpd.read_file(district_path, engine='fiona')
        print("使用fiona引擎成功加载行政区划数据")
    except:
        try:
            districts = gpd.read_file(district_path, engine='pyogrio')
            print("使用pyogrio引擎成功加载行政区划数据")
        except:
            districts = gpd.read_file(district_path)
            print("使用默认引擎成功加载行政区划数据")
    
    print(f"成功加载行政区划数据: 共{len(districts)}个区域")
    print(f"行政区划数据CRS: {districts.crs}")
    print(f"灯光数据CRS: {meta['crs']}")
    
    # 确保坐标系匹配
    if districts.crs != meta['crs']:
        print(f"转换坐标系: 从{districts.crs}到{meta['crs']}")
        districts = districts.to_crs(meta['crs'])
        print(f"转换后CRS: {districts.crs}")

except Exception as e:
    print(f"加载行政区划数据时出错: {e}")
    # 创建示例行政区划作为后备
    print("创建示例行政区划作为后备...")
    from shapely.geometry import Polygon
    example_districts = gpd.GeoDataFrame({
        'NAME': ['示例区1', '示例区2'],
        'geometry': [
            Polygon([(bounds.left, bounds.bottom), 
                     (bounds.left, bounds.top),
                     (bounds.right, bounds.top),
                     (bounds.right, bounds.bottom)]),
            Polygon([(bounds.left + (bounds.right - bounds.left)/2, bounds.bottom),
                     (bounds.left + (bounds.right - bounds.left)/2, bounds.top),
                     (bounds.right, bounds.top),
                     (bounds.right, bounds.bottom)])
        ]
    }, crs=meta['crs'])
    districts = example_districts

# 配置matplotlib使用Agg后端
plt.switch_backend('Agg')

# 设置中文字体
def set_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试使用SimHei字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        print("中文字体设置成功")
    except:
        print("警告: 中文字体设置失败，图表中的中文可能无法正常显示")

set_chinese_font()

# 计算每个行政区的平均灯光强度
print("开始计算行政区平均灯光强度...")
stats = zonal_stats(
    vectors=districts,
    raster=transformed_lights,
    affine=transform,
    stats=['mean', 'max', 'min', 'count'],
    geojson_out=True
)

# 转换为GeoDataFrame并添加统计数据
districts_gdf = gpd.GeoDataFrame.from_features(stats)
districts_gdf.rename(columns={'mean': 'light_intensity'}, inplace=True)

# 处理缺失值
districts_gdf['light_intensity'] = districts_gdf['light_intensity'].fillna(0)

# 根据灯光强度分类
if len(districts_gdf) > 0:
    bins = [0, low_threshold, high_threshold, max_val * 1.1]
    labels = ['低潜力', '中等潜力', '高潜力']
    
    districts_gdf['potential'] = pd.cut(
        districts_gdf['light_intensity'],
        bins=bins,
        labels=labels,
        include_lowest=True
    )
else:
    districts_gdf['potential'] = '低潜力'

# 打印结果
print("\n行政区潜力分析结果:")
if 'NAME' in districts_gdf.columns:
    print(districts_gdf[['NAME', 'light_intensity', 'potential']].sort_values('light_intensity', ascending=False))
else:
    districts_gdf['NAME'] = [f'区域{i+1}' for i in range(len(districts_gdf))]
    print(districts_gdf[['NAME', 'light_intensity', 'potential']].sort_values('light_intensity', ascending=False))

# ----------------------------
# 4. 可视化热点地图
# ----------------------------
# 创建输出目录
output_dir = r'd:\桌面\商业选址1\output_images'
os.makedirs(output_dir, exist_ok=True)

# 1. 灯光数据热力图
plt.figure(figsize=(12, 10))
im = plt.imshow(
    transformed_lights,
    cmap='viridis',
    vmin=0,
    vmax=np.percentile(transformed_lights, 95),
    interpolation='bilinear'
)
districts_gdf.boundary.plot(edgecolor='white', linewidth=0.8)
plt.colorbar(im, label='转换后灯光强度', shrink=0.7)
plt.title('转换后灯光强度分布 (2024年)', fontsize=16)
plt.axis('off')
plt.savefig(os.path.join(output_dir, '灯光强度分布图.png'), dpi=300, bbox_inches='tight')

# 2. 分类潜力区域
plt.figure(figsize=(12, 10))
ax = districts_gdf.plot(
    column='potential',
    legend=True,
    cmap='RdYlGn',  # 使用红-黄-绿渐变表示潜力
    legend_kwds={'loc': 'lower right', 'title': '潜力等级'},
    edgecolor='black',
    linewidth=0.5
)

# 添加分类信息
class_info = (f"分类阈值:\n低潜力: < {low_threshold:.1f}\n"
              f"中等潜力: {low_threshold:.1f} - {high_threshold:.1f}\n"
              f"高潜力: ≥ {high_threshold:.1f}")
plt.text(0.02, 0.02, class_info, 
         transform=ax.transAxes, color='black', fontsize=10,
         bbox=dict(facecolor='white', alpha=0.7))

# 添加行政区名称
for idx, row in districts_gdf.iterrows():
    name = row.get('NAME', f'区域{idx}')
    centroid = row.geometry.centroid
    plt.annotate(
        text=name,
        xy=(centroid.x, centroid.y),
        xytext=(0, 0),
        textcoords="offset points",
        ha='center',
        va='center',
        fontsize=9,
        color='black',
        bbox=dict(boxstyle="round,pad=0.2", fc='white', alpha=0.7)
    )

plt.title('商业选址潜力分类', fontsize=16)
plt.axis('off')
plt.savefig(os.path.join(output_dir, '商业潜力地图.png'), dpi=300, bbox_inches='tight')

# 3. 行政区灯光强度对比
plt.figure(figsize=(14, 8))
districts_sorted = districts_gdf.sort_values('light_intensity', ascending=False)

# 使用条形图
bars = plt.bar(
    x=districts_sorted['NAME'],
    height=districts_sorted['light_intensity'],
    color=['#4daf4a' if p == '高潜力' else '#ff7f00' if p == '中等潜力' else '#e41a1c' for p in districts_sorted['potential']]
)

# 添加数值标签
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height,
             f'{height:.2f}', ha='center', va='bottom', fontsize=9)

# 添加分类阈值线
plt.axhline(y=low_threshold, color='orange', linestyle='--', alpha=0.7)
plt.axhline(y=high_threshold, color='green', linestyle='--', alpha=0.7)
plt.text(len(districts_sorted)*1.05, low_threshold, f'低阈值: {low_threshold:.1f}', 
         ha='left', va='center', color='orange')
plt.text(len(districts_sorted)*1.05, high_threshold, f'高阈值: {high_threshold:.1f}', 
         ha='left', va='center', color='green')

plt.title('各行政区灯光强度对比', fontsize=16)
plt.xlabel('行政区', fontsize=12)
plt.ylabel('灯光强度', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
plt.savefig(os.path.join(output_dir, '行政区灯光强度对比.png'), dpi=300)

# 4. 创建图例说明
plt.figure(figsize=(8, 4))
plt.axis('off')
legend_text = (
    "商业选址潜力分析报告\n\n"
    f"分析日期: {pd.Timestamp.now().strftime('%Y-%m-%d')}\n"
    f"灯光数据统计:\n"
    f"  最大值: {max_val:.2f}\n"
    f"  最小值: {min_val:.2f}\n"
    f"  平均值: {mean_val:.2f}\n"
    f"  非零像素: {non_zero_ratio:.1f}%\n\n"
    f"区域潜力分类:\n"
    f"  无灯光区域: {no_light:.1f}%\n"
    f"  低潜力区域: {low_percent:.1f}%\n"
    f"  中等潜力区域: {med_percent:.1f}%\n"
    f"  高潜力区域: {high_percent:.1f}%\n\n"
    f"分类阈值:\n"
    f"  低潜力: < {low_threshold:.1f}\n"
    f"  中等潜力: {low_threshold:.1f} - {high_threshold:.1f}\n"
    f"  高潜力: ≥ {high_threshold:.1f}"
)

plt.text(0.1, 0.5, legend_text, fontsize=10, va='center')
plt.savefig(os.path.join(output_dir, '分析报告说明.png'), dpi=300, bbox_inches='tight')

print(f"所有图表已保存至: {output_dir}")

# ----------------------------
# 5. 保存分析结果
# ----------------------------
# 重命名列以避免冲突，并确保列名长度不超过10个字符
districts_gdf = districts_gdf.rename(columns={
    'light_intensity': 'light_int',
    'potential': 'pot_level'
})

# 将Categorical类型转换为字符串
districts_gdf['pot_level'] = districts_gdf['pot_level'].astype(str)

# 确保中文正常显示
districts_gdf['pot_level'] = districts_gdf['pot_level'].str.encode('utf-8').str.decode('utf-8')

# 保存为Shapefile
output_shapefile = os.path.join(output_dir, 'business_potential_districts.shp')
try:
    districts_gdf.to_file(output_shapefile, encoding='utf-8')
    print(f"分析结果已保存为Shapefile: {output_shapefile}")
except Exception as e:
    print(f"保存Shapefile时出错: {e}")
    # 保存为GeoJSON作为后备
    output_geojson = os.path.join(output_dir, 'business_potential_districts.geojson')
    districts_gdf.to_file(output_geojson, driver='GeoJSON', encoding='utf-8')
    print(f"分析结果已保存为GeoJSON: {output_geojson}")

# 保存为Excel
output_excel = os.path.join(output_dir, '商业选址分析报告.xlsx')
try:
    with pd.ExcelWriter(output_excel, engine='openpyxl') as writer:
        # 行政区分析结果
        report_df = districts_gdf[['NAME', 'light_int', 'pot_level']].sort_values('light_int', ascending=False)
        report_df.columns = ['行政区名称', '灯光强度', '潜力等级']
        report_df.to_excel(writer, sheet_name='行政区分析', index=False)
        
        # 整体统计
        stats_df = pd.DataFrame({
            '指标': ['最大灯光强度', '最小灯光强度', '平均灯光强度', '非零像素比例',
                    '无灯光区域占比', '低潜力区域占比', '中等潜力区域占比', '高潜力区域占比'],
            '数值': [max_val, min_val, mean_val, non_zero_ratio,
                   no_light, low_percent, med_percent, high_percent]
        })
        stats_df.to_excel(writer, sheet_name='整体统计', index=False)
        
        # 阈值信息
        thresholds_df = pd.DataFrame({
            '阈值类型': ['低潜力上限', '高潜力下限'],
            '值': [low_threshold, high_threshold]
        })
        thresholds_df.to_excel(writer, sheet_name='分类阈值', index=False)
    
    print(f"分析报告已保存为Excel: {output_excel}")
    print("商业选址潜力分析完成！")
except Exception as e:
    print(f"保存Excel报告时出错: {e}")